<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Product preview card component for Gabrielle E<PERSON> Eau De Parfum">
  <meta name="theme-color" content="#f2ebe3">

  <link rel="icon" type="image/png" sizes="32x32" href="./images/favicon-32x32.png">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Fraunces:opsz,wght@9..144,700&family=Montserrat:wght@400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
  <title>Frontend Mentor | Product preview card component</title>

  <!-- Attribution styles -->
  <style>
    .attribution {
      font-size: 11px;
      text-align: center;
      margin-top: 20px;
    }

    .attribution a {
      color: hsl(228, 45%, 44%);
    }
  </style>
</head>

<body>
  <main class="container">
    <article class="card" itemscope itemtype="https://schema.org/Product">
      <div class="card-image">
        <picture>
          <source media="(min-width: 768px)" srcset="./images/image-product-desktop.jpg">
          <source media="(max-width: 767px)" srcset="./images/image-product-mobile.jpg">
          <img src="./images/image-product-mobile.jpg" alt="Gabrielle Essence Eau De Parfum bottle" width="540" height="720"
            loading="lazy" itemprop="image" />
        </picture>
      </div>
      <section class="content">
        <p class="title">Perfume</p>
        <h1 class="name" itemprop="name">Gabrielle Essence Eau De Parfum</h1>
        <p class="desc" itemprop="description">
          A floral, solar and voluptuous interpretation composed by Olivier Polge,
          Perfumer-Creator for the House of CHANEL.
        </p>
        <div class="price" itemprop="offers" itemscope itemtype="https://schema.org/Offer">
          <span class="discount" itemprop="price">$149.99</span>
          <span class="original">$169.99</span>
          <meta itemprop="priceCurrency" content="USD">
        </div>
        <button class="btn" aria-label="Add to cart">
          <img src="./images/icon-cart.svg" alt="" width="16" height="16" loading="lazy" aria-hidden="true">
          <span class="btn-text">Add to cart</span>
        </button>
      </section>
    </article>
  </main>
</body>

</html>