/* CSS变量定义 - 使用回退值确保兼容性 */
:root {
  --primary-color: hsl(158, 36%, 37%);
  --primary-hover: hsl(158, 42%, 18%);
  --bg-color: hsl(30, 38%, 92%);
  --text-color: hsl(228, 12%, 48%);
  --heading-color: hsl(212, 21%, 14%);
  --white-color: hsl(0, 0%, 100%);
  --font-size: 14px;
  --border-radius: 10px;
  --spacing: 1.5rem;
  --transition: all 0.3s ease;
}

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: hsl(30, 38%, 92%); /* 回退颜色 */
  background-color: var(--bg-color, hsl(30, 38%, 92%));
  font-size: 14px;
  font-size: var(--font-size, 14px);
  font-family: 'Montserrat', sans-serif;
  color: hsl(228, 12%, 48%);
  color: var(--text-color, hsl(228, 12%, 48%));
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  width: 100%;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

.card {
  width: 90%;
  max-width: 375px;
  display: flex;
  flex-direction: column;
  background-color: hsl(0, 0%, 100%);
  background-color: var(--white-color, hsl(0, 0%, 100%));
  border-radius: 10px;
  border-radius: var(--border-radius, 10px);
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.card-image {
  width: 100%;
}

.card img {
  width: 100%;
  height: auto;
  display: block;
}

.content {
  padding: 1.5rem;
  padding: var(--spacing, 1.5rem);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.title {
  text-transform: uppercase;
  letter-spacing: 0.3em;
  font-size: 0.8rem;
  color: hsl(228, 12%, 48%);
  color: var(--text-color, hsl(228, 12%, 48%));
  margin-bottom: 0.5rem;
}

.name {
  font-family: 'Fraunces', serif;
  font-size: 2rem;
  line-height: 1;
  color: hsl(212, 21%, 14%);
  color: var(--heading-color, hsl(212, 21%, 14%));
  margin-bottom: 1rem;
}

.desc {
  line-height: 1.6;
  margin-bottom: 1rem;
}

.price {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 0.5rem 0 1.5rem;
}

.discount {
  font-family: 'Fraunces', serif;
  font-size: 2rem;
  font-weight: 700;
  color: hsl(158, 36%, 37%);
  color: var(--primary-color, hsl(158, 36%, 37%));
}

.original {
  text-decoration: line-through;
  font-size: 0.9rem;
}

.btn {
  background-color: hsl(158, 36%, 37%);
  background-color: var(--primary-color, hsl(158, 36%, 37%));
  color: hsl(0, 0%, 100%);
  color: var(--white-color, hsl(0, 0%, 100%));
  border: none;
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  transition: var(--transition, all 0.3s ease);
  width: 100%;
}

.btn:hover, .btn:focus {
  background-color: hsl(158, 42%, 18%);
  background-color: var(--primary-hover, hsl(158, 42%, 18%));
  outline: none;
}

.btn img{
  width: 20px;
}

/* 响应式布局 */
@media screen and (min-width: 768px) {
  .card {
    flex-direction: row;
    max-width: 600px;
  }

  .card-image {
    flex: 1;
  }

  .card img {
    height: 100%;
    object-fit: cover;
  }

  .content {
    flex: 1;
  }
}