@supports(--css:variables) {
  :root {
    --primary-color: hsl(158, 36%, 37%);
    --secondary-color: hsl(158, 42%, 18%);
    --bg-color: hsl(30, 38%, 92%);
    --text-color: hsl(228, 12%, 48%);
    --black-color: hsl(212, 21%, 14%);
    --white-color: hsl(0, 0%, 100%);

    --font-size: 14px;
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: hsl(30, 38%, 92%);
  background-color: var(--bg-color);
  font-size: 14px;
  font-size: var(--font-size);
}

.container {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center
}

.card {
  width: 90%;
  max-width: 375px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: hsl(0, 0%, 100%);
  background-color: var(--white-color);
}

.card img{
  width: 100%;
  height: auto;
}

@media screen and (min-width: 768px){
  .card{
    flex-direction: row;
    max-width: 568px;
  }

  .card img{
    flex: 1 0 auto;
    width: 100%;
    height: 100%;
  }
  .content{
    flex: 0 0 50%;
  }
}