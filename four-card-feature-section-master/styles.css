@supports (--css: variables) {
  :root{
    --red-color: hsl(0, 78%, 62%);

  }
}

*{
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body{
  font-size: 15px;
  overflow: auto;
}

.container{
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  
}



.cards{
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card{
  border: 1px solid #000;
  margin-top: 2rem;
  flex: 1 1 100%;
  width: 100%;
}

@media screen and (min-width: 768px){
  .container{
    padding: 4rem;
  }
  .cards{
    display: block;
    margin-top: 300px;
  }
  .card{
    width: 300px;
    display: inline-block;
  }
  .card:nth-child(2){
    margin-top: -300px;
  }
}