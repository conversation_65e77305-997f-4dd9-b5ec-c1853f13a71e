/* CSS变量定义 - 使用回退值确保兼容性 */
:root {
  /* 主色调 */
  --color-red: hsl(0, 78%, 62%);
  --color-cyan: hsl(180, 62%, 55%);
  --color-orange: hsl(34, 97%, 64%);
  --color-blue: hsl(212, 86%, 64%);

  /* 中性色 */
  --color-very-dark-blue: hsl(234, 12%, 34%);
  --color-grayish-blue: hsl(229, 6%, 66%);
  --color-very-light-gray: hsl(0, 0%, 98%);
  --color-white: hsl(0, 0%, 100%);

  /* 字体 */
  --font-family: 'Poppins', sans-serif;
  --font-size-base: 15px;
  --font-weight-light: 200;
  --font-weight-normal: 400;
  --font-weight-semibold: 600;

  /* 间距 */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-xxl: 4rem;

  /* 其他 */
  --border-radius: 8px;
  --box-shadow: 0 15px 30px -11px rgba(131, 166, 210, 0.5);
  --transition: all 0.3s ease;
}

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  font-family: var(--font-family, 'Poppins', sans-serif);
  font-size: 15px;
  font-size: var(--font-size-base, 15px);
  font-weight: 400;
  font-weight: var(--font-weight-normal, 400);
  color: hsl(229, 6%, 66%);
  color: var(--color-grayish-blue, hsl(229, 6%, 66%));
  background-color: hsl(0, 0%, 98%);
  background-color: var(--color-very-light-gray, hsl(0, 0%, 98%));
  line-height: 1.6;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  padding: var(--spacing-lg, 2rem);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Hero区域样式 */
.hero {
  text-align: center;
  margin-bottom: 4rem;
  margin-bottom: var(--spacing-xxl, 4rem);
  max-width: 540px;
}

.hero-title {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  margin-bottom: var(--spacing-sm, 1rem);
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.hero-title-light {
  font-weight: 200;
  font-weight: var(--font-weight-light, 200);
  color: hsl(234, 12%, 34%);
  color: var(--color-very-dark-blue, hsl(234, 12%, 34%));
}

.hero-title-bold {
  font-weight: 600;
  font-weight: var(--font-weight-semibold, 600);
  color: hsl(234, 12%, 34%);
  color: var(--color-very-dark-blue, hsl(234, 12%, 34%));
}

.hero-description {
  font-size: 0.95rem;
  line-height: 1.7;
}

/* 卡片容器 */
.cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  gap: var(--spacing-md, 1.5rem);
  width: 100%;
  max-width: 1110px;
}

/* 卡片基础样式 */
.card {
  background-color: hsl(0, 0%, 100%);
  background-color: var(--color-white, hsl(0, 0%, 100%));
  border-radius: 8px;
  border-radius: var(--border-radius, 8px);
  box-shadow: 0 15px 30px -11px rgba(131, 166, 210, 0.5);
  box-shadow: var(--box-shadow, 0 15px 30px -11px rgba(131, 166, 210, 0.5));
  padding: 2rem;
  padding: var(--spacing-lg, 2rem);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  transition: var(--transition, all 0.3s ease);
  border-top: 4px solid transparent;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px -11px rgba(131, 166, 210, 0.6);
}

/* 卡片顶部边框颜色 */
.card-supervisor {
  border-top-color: hsl(180, 62%, 55%);
  border-top-color: var(--color-cyan, hsl(180, 62%, 55%));
}

.card-team-builder {
  border-top-color: hsl(0, 78%, 62%);
  border-top-color: var(--color-red, hsl(0, 78%, 62%));
}

.card-karma {
  border-top-color: hsl(34, 97%, 64%);
  border-top-color: var(--color-orange, hsl(34, 97%, 64%));
}

.card-calculator {
  border-top-color: hsl(212, 86%, 64%);
  border-top-color: var(--color-blue, hsl(212, 86%, 64%));
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  font-weight: var(--font-weight-semibold, 600);
  color: hsl(234, 12%, 34%);
  color: var(--color-very-dark-blue, hsl(234, 12%, 34%));
  margin-bottom: 0.5rem;
  margin-bottom: var(--spacing-xs, 0.5rem);
}

.card-description {
  font-size: 0.85rem;
  line-height: 1.7;
  margin-bottom: 2rem;
  margin-bottom: var(--spacing-lg, 2rem);
}

.card-icon {
  display: block;
  margin-left: auto;
  width: 64px;
  height: 64px;
}

/* 响应式设计 */
@media screen and (min-width: 768px) {
  .hero-title {
    font-size: 2.25rem;
  }

  .cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    gap: var(--spacing-lg, 2rem);
  }
}

@media screen and (min-width: 1024px) {
  .container {
    padding: 4rem;
    padding: var(--spacing-xxl, 4rem);
  }

  .cards {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 2rem;
    gap: var(--spacing-lg, 2rem);
  }

  /* 特殊的网格布局 */
  .card-supervisor {
    grid-column: 1;
    grid-row: 2 / 4;
  }

  .card-team-builder {
    grid-column: 2;
    grid-row: 1 / 3;
  }

  .card-karma {
    grid-column: 2;
    grid-row: 3 / 5;
  }

  .card-calculator {
    grid-column: 3;
    grid-row: 2 / 4;
  }
}