/* 使用CSS变量定义主题颜色和尺寸 */
:root {
  /* 颜色 */
  --color-bg: hsl(0, 0%, 8%);
  --color-card: hsl(0, 0%, 12%);
  --color-text: hsl(0, 0%, 100%);
  --color-text-secondary: hsl(0, 0%, 75%);
  --color-link-bg: hsl(0, 0%, 20%);
  --color-link-hover: hsl(75, 94%, 57%);

  /* 尺寸 */
  --card-width: 320px;
  --border-radius: 12px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;

  /* 字体 */
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 24px;
  --font-weight-normal: 400;
  --font-weight-bold: 600;
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: var(--color-bg);
  color: var(--color-text);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: var(--spacing-md);
}

/* 卡片样式 */
.card {
  background-color: var(--color-card);
  border-radius: var(--border-radius);
  width: 100%;
  max-width: var(--card-width);
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* 头像样式 */
.avatar {
  border-radius: 50%;
  margin-bottom: var(--spacing-md);
  object-fit: cover;
}

/* 名称样式 */
.name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
}

/* 位置样式 */
.location {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-md);
}

/* 简介样式 */
.bio {
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-lg);
  color: var(--color-text-secondary);
}

/* 社交链接列表 */
.social-links {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: var(--spacing-md);
  list-style: none;
}

/* 社交链接项 */
.social-links li {
  width: 100%;
}

/* 社交链接样式 */
.social-links a {
  display: block;
  width: 100%;
  padding: var(--spacing-md);
  background-color: var(--color-link-bg);
  color: var(--color-text);
  text-decoration: none;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-bold);
  transition: all 0.3s ease;
}

.social-links a:hover,
.social-links a:focus {
  background-color: var(--color-link-hover);
  color: var(--color-bg);
  transform: translateY(-2px);
}

/* 响应式设计 */
@media screen and (min-width: 768px) {
  :root {
    --card-width: 380px;
  }

  .card {
    padding: var(--spacing-lg) var(--spacing-lg) calc(var(--spacing-lg) * 1.5);
  }
}

/* 兼容性回退 */
@supports not (--css: variables) {
  body {
    background-color: hsl(0, 0%, 8%);
    color: hsl(0, 0%, 100%);
  }

  .card {
    background-color: hsl(0, 0%, 12%);
  }

  .location, .bio {
    color: hsl(0, 0%, 75%);
  }

  .social-links a {
    background-color: hsl(0, 0%, 20%);
  }

  .social-links a:hover,
  .social-links a:focus {
    background-color: hsl(75, 94%, 57%);
    color: hsl(0, 0%, 8%);
  }
}