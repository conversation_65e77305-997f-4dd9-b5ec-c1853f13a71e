<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Blog preview card showcasing HTML & CSS foundations article">
  <meta name="keywords" content="blog, HTML, CSS, web development, frontend">
  <meta name="author" content="Grey Hooper">

  <link rel="icon" type="image/png" sizes="32x32" href="./assets/images/favicon-32x32.png">
  <link rel="stylesheet" href="./styles.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Figtree:wght@500;800&display=swap" rel="stylesheet">

  <title>Frontend Mentor | Blog preview card</title>

  <!-- CSS变量Polyfill (可选) -->
  <script>
    // 检测浏览器是否支持CSS变量
    var supportsCssVars = function() {
      return window.CSS && window.CSS.supports && window.CSS.supports('(--a: 0)');
    };

    // 如果不支持CSS变量，加载polyfill
    if (!supportsCssVars()) {
      var link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://cdn.jsdelivr.net/npm/css-vars-ponyfill@2/dist/css-vars-ponyfill.min.css';
      document.head.appendChild(link);

      var script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/css-vars-ponyfill@2/dist/css-vars-ponyfill.min.js';
      script.onload = function() {
        cssVars({
          // 选项
          onlyLegacy: true, // 只在不支持的浏览器中运行
          preserveStatic: true,
          preserveVars: false
        });
      };
      document.head.appendChild(script);
    }
  </script>
</head>
<body>
  <a href="#main-content" class="skip-link">Skip to main content</a>

  <main id="main-content">
    <article class="blog-card">
      <img
        src="./assets/images/illustration-article.svg"
        alt="Illustration for HTML & CSS foundations article"
        class="card-image"
        width="336"
        height="201"
        loading="lazy"
      >
      <button class="category-tag">Learning</button>
      <time datetime="2023-12-21" class="publish-date">Published 21 Dec 2023</time>

      <div class="article-content">
        <h1 class="article-title">HTML & CSS foundations</h1>
        <p class="article-excerpt">These languages are the backbone of every website, defining structure, content, and presentation.</p>
      </div>

      <footer class="author-info">
        <img
          src="./assets/images/image-avatar.webp"
          alt="Grey Hooper's profile picture"
          class="author-avatar"
          width="32"
          height="32"
          loading="lazy"
        >
        <span class="author-name">Grey Hooper</span>
      </footer>
    </article>
  </main>
</body>
</html>