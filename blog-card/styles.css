/*
 * Blog Card Component Styles
 *
 * 注意：本CSS使用了CSS变量（自定义属性）
 * 为了兼容不支持CSS变量的浏览器（如IE11），
 * 我们为每个使用变量的属性都提供了回退值
 */

/* CSS变量兼容性检测 */
@supports (--css: variables) {
  /* 只有支持CSS变量的浏览器才会应用这些样式 */
  :root {
    /* 颜色变量 */
    --color-yellow: hsl(47, 88%, 63%);
    --color-white: hsl(0, 0%, 100%);
    --color-gray-500: hsl(0, 0%, 42%);
    --color-gray-950: hsl(0, 0%, 7%);

    /* 尺寸变量 */
    --border-radius: 16px;
    --card-padding: 24px;
    --font-size-base: 16px;
    --font-size-small: 14px;
    --font-size-heading: 20px;
    --line-height: 1.5;
  }
}

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Figtree', sans-serif;
  font-size: 16px;
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  line-height: var(--line-height);
  background-color: hsl(47, 88%, 63%);
  background-color: var(--color-yellow);
  color: hsl(0, 0%, 7%);
  color: var(--color-gray-950);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

/* 无障碍跳过链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background: hsl(0, 0%, 7%);
  background: var(--color-gray-950);
  color: hsl(0, 0%, 100%);
  color: var(--color-white);
  padding: 8px;
  z-index: 100;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 0;
}

/* 主要内容区域 */
main {
  width: 100%;
  max-width: 375px;
  margin: 0 auto;
}

/* 博客卡片 */
.blog-card {
  background-color: hsl(0, 0%, 100%);
  background-color: var(--color-white);
  border-radius: 16px;
  border-radius: var(--border-radius);
  padding: 24px;
  padding: var(--card-padding);
  box-shadow: 8px 8px 0 hsl(0, 0%, 7%);
  box-shadow: 8px 8px 0 var(--color-gray-950);
  border: 1px solid hsl(0, 0%, 7%);
  border: 1px solid var(--color-gray-950);
  transition: transform 0.3s ease;
}

.blog-card:hover {
  transform: translate(-5px, -5px);
  box-shadow: 13px 13px 0 hsl(0, 0%, 7%);
  box-shadow: 13px 13px 0 var(--color-gray-950);
}

/* 卡片封面图 */
.card-image {
  width: 100%;
  height: auto;
  border-radius: 8px;
  border-radius: calc(var(--border-radius) - 8px);
  margin-bottom: 20px;
}

/* 分类标签 */
.category-tag {
  display: inline-block;
  background-color: hsl(47, 88%, 63%);
  background-color: var(--color-yellow);
  color: hsl(0, 0%, 7%);
  color: var(--color-gray-950);
  font-weight: 800;
  padding: 6px 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border: none;
  cursor: pointer;
}

/* 发布日期 */
.publish-date {
  font-size: 14px;
  font-size: var(--font-size-small);
  color: hsl(0, 0%, 7%);
  color: var(--color-gray-950);
  margin-bottom: 16px;
  font-weight: 500;
}

/* 文章标题 */
.article-title {
  font-size: 20px;
  font-size: var(--font-size-heading);
  font-weight: 800;
  margin-bottom: 12px;
  color: hsl(0, 0%, 7%);
  color: var(--color-gray-950);
}

.article-title:hover {
  color: hsl(47, 88%, 63%);
  color: var(--color-yellow);
}

/* 文章摘要 */
.article-excerpt {
  color: hsl(0, 0%, 42%);
  color: var(--color-gray-500);
  margin-bottom: 20px;
}

/* 作者信息 */
.author-info {
  display: flex;
  align-items: center;
  margin-top: 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 20px;
}

.author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 12px;
}

.author-name {
  font-weight: 800;
  color: hsl(0, 0%, 7%);
  color: var(--color-gray-950);
}

/* 页脚 */
.attribution {
  font-size: 11px;
  text-align: center;
  margin-top: 20px;
  color: hsl(0, 0%, 7%);
  color: var(--color-gray-950);
}

.attribution a {
  color: hsl(0, 0%, 7%);
  color: var(--color-gray-950);
  font-weight: 800;
}

/* 响应式设计 */
@media screen and (min-width: 768px) {
  /* CSS变量重定义 */
  :root {
    --font-size-heading: 24px;
    --card-padding: 32px;
  }

  /* 回退值 */
  .article-title {
    font-size: 24px;
  }

  .blog-card {
    padding: 32px;
  }

  main {
    max-width: 450px;
  }
}
