<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0"> <!-- displays site properly based on user's device -->
  <meta name="description" content="QR code component to improve your front-end skills">
  <meta name="keywords" content="QR code, frontend, web development">

  <link rel="icon" type="image/png" sizes="32x32" href="./images/favicon-32x32.png">
  <link rel="stylesheet" href="./index.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <title>QR code component</title>

  <!-- CSS变量Polyfill (可选) -->
  <script>
    // 检测浏览器是否支持CSS变量
    var supportsCssVars = function() {
      return window.CSS && window.CSS.supports && window.CSS.supports('(--a: 0)');
    };

    // 如果不支持CSS变量，加载polyfill
    if (!supportsCssVars()) {
      var link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://cdn.jsdelivr.net/npm/css-vars-ponyfill@2/dist/css-vars-ponyfill.min.css';
      document.head.appendChild(link);

      var script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/css-vars-ponyfill@2/dist/css-vars-ponyfill.min.js';
      script.onload = function() {
        cssVars({
          // 选项
          onlyLegacy: true, // 只在不支持的浏览器中运行
          preserveStatic: true,
          preserveVars: false
        });
      };
      document.head.appendChild(script);
    }
  </script>
</head>
<body>
<a href="#main-content" class="skip-link">Skip to main content</a>
<main id="main-content">
  <article class="container" aria-labelledby="qr-title">
    <img src="images/image-qr-code.png" alt="QR code to Frontend Mentor website" width="288" height="288" loading="lazy">
    <section class="text-wrapper">
      <h1 id="qr-title">Improve your front-end skills by building projects</h1>
      <p>Scan the QR code to visit Frontend Mentor and take your coding skills to the next level</p>
    </section>
  </article>
</main>
</body>
</html>