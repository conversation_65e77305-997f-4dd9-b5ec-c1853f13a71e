/*
 * QR Code Component Styles
 *
 * 注意：本CSS使用了CSS变量（自定义属性）
 * 为了兼容不支持CSS变量的浏览器（如IE11），
 * 我们为每个使用变量的属性都提供了回退值
 */

@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@400;700&display=swap");

/* CSS变量兼容性检测 */
@supports (--css: variables) {
  /* 只有支持CSS变量的浏览器才会应用这些样式 */
  :root {
    /* 颜色变量 */
    --color-background: hsl(212, 45%, 89%);
    --color-white: hsl(0, 0%, 100%);
    --color-heading: hsl(218, 44%, 22%);
    --color-paragraph: hsl(216, 15%, 48%);

    /* 尺寸变量 */
    --container-max-width: 320px;
    --border-radius-container: 20px;
    --border-radius-image: 16px;
    --padding-container: 16px 16px 24px 16px;
    --font-size-base: 15px;
    --font-size-heading: 20px;
    --line-height: 1.3;
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  /* 回退值 */
  background: hsl(212, 45%, 89%);
  background: var(--color-background);
  font-size: 15px;
  font-size: var(--font-size-base);
  min-height: 100vh;
  width: 100%;
  font-family: 'Outfit', sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 无障碍跳过链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background: hsl(218, 44%, 22%);
  background: var(--color-heading);
  color: hsl(0, 0%, 100%);
  color: var(--color-white);
  padding: 8px;
  z-index: 100;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 0;
}

main {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.container {
  width: 90%;
  max-width: 320px;
  max-width: var(--container-max-width);
  border-radius: 20px;
  border-radius: var(--border-radius-container);
  background-color: hsl(0, 0%, 100%);
  background-color: var(--color-white);
  box-shadow: 0px 4px 6px -4px rgba(0, 0, 0, 0.1), 0px 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 16px 16px 24px 16px;
  padding: var(--padding-container);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.container img {
  width: 100%;
  max-width: 288px;
  height: auto;
  border-radius: 16px;
  border-radius: var(--border-radius-image);
}

.container .text-wrapper {
  text-align: center;
  padding: 0 10px;
  margin-top: 14px;
}

.text-wrapper h1 {
  font-size: 20px;
  font-size: var(--font-size-heading);
  font-weight: 700;
  color: hsl(218, 44%, 22%);
  color: var(--color-heading);
  line-height: 1.3;
  line-height: var(--line-height);
}

.text-wrapper p {
  margin-top: 16px;
  font-weight: 400;
  color: hsl(216, 15%, 48%);
  color: var(--color-paragraph);
  line-height: 1.3;
  line-height: var(--line-height);
}

/* 移动设备适配 */
@media screen and (max-width: 375px) {
  /* CSS变量重定义 */
  :root {
    --container-max-width: 290px;
    --padding-container: 14px 14px 20px 14px;
  }

  /* 回退值 */
  .container {
    max-width: 290px;
    padding: 14px 14px 20px 14px;
  }

  .container img {
    max-width: 262px;
  }

  .text-wrapper p {
    font-size: 14px;
  }
}

@media screen and (max-width: 320px) {
  /* CSS变量重定义 */
  :root {
    --container-max-width: 260px;
    --padding-container: 12px 12px 18px 12px;
    --font-size-heading: 18px;
  }

  /* 回退值 */
  .container {
    max-width: 260px;
    padding: 12px 12px 18px 12px;
  }

  .container img {
    max-width: 236px;
  }

  .text-wrapper h1 {
    font-size: 18px;
    margin-bottom: 12px;
  }

  .text-wrapper p {
    font-size: 13px;
  }
}

/* 平板设备适配 (768px - 1023px) */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  /* CSS变量重定义 */
  :root {
    --container-max-width: 400px;
    --padding-container: 20px 20px 30px 20px;
    --font-size-heading: 24px;
  }

  /* 回退值 */
  .container {
    max-width: 400px;
    padding: 20px 20px 30px 20px;
  }

  .container img {
    max-width: 360px;
  }

  .container .text-wrapper {
    margin-top: 25px;
  }

  .text-wrapper h1 {
    font-size: 24px;
    margin-bottom: 20px;
  }

  .text-wrapper p {
    font-size: 18px;
    line-height: 1.5;
  }
}

/* 大屏幕设备 (1024px 及以上) */
@media screen and (min-width: 1024px) {
  /* CSS变量重定义 */
  :root {
    --font-size-heading: 22px;
  }

  /* 回退值 */
  .text-wrapper h1 {
    font-size: 22px;
  }
}