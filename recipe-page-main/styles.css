/* 全局样式 */
:root {
  --primary-color: hsla(31, 81%, 79%, 0.743);
  --secondary-color: #2c3e50;
  --text-color: #333;
  --light-gray: #f5f5f5;
  --border-color: #ddd;
  --accent-color: #e74c3c;
  --font-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-secondary: Georgia, 'Times New Roman', Times, serif;
  --box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--primary-color);
  padding: 0;
  margin: 0;
}

/* 容器样式 */
.recipe-container {
  max-width: 800px;
  margin: 0 auto;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  padding: 2rem;
}

/* 标题和描述 */
.recipe-header {
  padding: 0 0 1.5rem 0;
}

.recipe-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  margin-bottom: 1.5rem;
}

.recipe-title {
  font-family: var(--font-secondary);
  font-size: 2.2rem;
  color: var(--secondary-color);
  margin: 0 1.5rem 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
}

.recipe-description {
  font-size: 1.1rem;
  line-height: 1.7;
  margin: 0 1.5rem 1.5rem;
  color: #555;
}

/* 准备时间 */
.recipe-metadata {
  background-color: var(--light-gray);
  padding: 1.5rem;
  margin: 0 1.5rem;
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}

.prep-time h2 {
  font-size: 1.3rem;
  margin-bottom: 0.8rem;
  color: var(--secondary-color);
}

.prep-time ul {
  list-style-type: none;
}

.prep-time li {
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.prep-time li::before {
  content: "⏱";
  margin-right: 0.5rem;
  color: var(--primary-color);
}

/* 配料部分 */
.recipe-ingredients {
  padding: 1.5rem;
  margin: 1.5rem;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.recipe-ingredients h2 {
  font-size: 1.5rem;
  margin-bottom: 1.2rem;
  color: var(--secondary-color);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 0.5rem;
}

.ingredients-container h3 {
  font-size: 1.2rem;
  margin: 1rem 0 0.8rem;
  color: var(--accent-color);
}

.ingredients-container ul {
  list-style-type: none;
  margin-left: 1rem;
}

.ingredients-container li {
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 1.5rem;
}

.ingredients-container li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-size: 1.2rem;
}

/* 步骤部分 */
.recipe-instructions {
  padding: 1.5rem;
  margin: 1.5rem;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.recipe-instructions h2 {
  font-size: 1.5rem;
  margin-bottom: 1.2rem;
  color: var(--secondary-color);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 0.5rem;
}

.recipe-instructions ol {
  margin-left: 1.5rem;
}

.recipe-instructions li {
  margin-bottom: 1.2rem;
}

.recipe-instructions p {
  margin-bottom: 0.5rem;
}

/* 营养信息 */
.recipe-nutrition {
  padding: 1.5rem;
  margin: 1.5rem;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.recipe-nutrition h2 {
  font-size: 1.5rem;
  margin-bottom: 1.2rem;
  color: var(--secondary-color);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 0.5rem;
}

.recipe-nutrition table {
  width: 100%;
  border-collapse: collapse;
}

.recipe-nutrition th, .recipe-nutrition td {
  padding: 0.8rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.recipe-nutrition th {
  font-weight: bold;
  color: var(--secondary-color);
}

/* 页脚 */
.attribution {
  text-align: center;
  padding: 1.5rem;
  font-size: 0.9rem;
  color: #777;
}

.attribution a {
  color: var(--accent-color);
  text-decoration: none;
}

.attribution a:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  body{
    background-color: var(--light-gray);
  }
  .recipe-container {
    margin: 0;
    border-radius: 0;
    padding: 0;
  }

  .recipe-title {
    font-size: 1.8rem;
  }

  .recipe-image {
    max-width: 100%;
    border-radius: 0;
  }
  .recipe-ingredients,
  .recipe-instructions,
  .recipe-nutrition,
  .recipe-metadata {
    margin: 1rem;
    padding: 1rem;
  }
}

/* 兼容性处理 */
@supports not (--primary-color: #f8b400) {
  /* 为不支持CSS变量的浏览器提供回退样式 */
  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
  }

  .recipe-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  .recipe-title {
    font-family: Georgia, 'Times New Roman', Times, serif;
    color: #2c3e50;
    border-bottom: 2px solid #f8b400;
  }

  .recipe-metadata {
    border-left: 4px solid #f8b400;
  }

  .prep-time li::before,
  .ingredients-container li::before {
    color: #f8b400;
  }

  .ingredients-container h3 {
    color: #e74c3c;
  }

  .attribution a {
    color: #e74c3c;
  }
}