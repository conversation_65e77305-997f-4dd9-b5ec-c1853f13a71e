@supports (--css: variables) {
  :root {
    --color-purple-50: hsl(260, 100%, 95%);
    --color-purple-300: hsl(264, 82%, 80%);
    --color-purple-500: hsl(263, 55%, 52%);
    --color-white: hsl(0, 0%, 100%);
    --color-gray-100: hsl(214, 17%, 92%);
    --color-gray-200: hsl(0, 0%, 81%);
    --color-gray-400: hsl(224, 10%, 45%);
    --color-gray-500: hsl(217, 19%, 35%);
    --color-dark-blue: hsl(219, 29%, 14%);
    --color-black: hsl(0, 0%, 7%);
    --font-family: 'Inter', sans-serif;
    --font-size-base: 13px;
    --font-weight-normal: 400;
    --font-weight-bold: 700;
    --spacing-sm: 0.5rem;
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.container {
  padding: 3rem;
}

.card {
  border-radius: 8px;
  margin-bottom: 2rem;
  padding: 3rem;
}

.card img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 1rem;

}

.card-1 {
  background-color: hsl(263, 55%, 52%);
  background-color: var(--color-purple-500);
  color: hsl(0, 0%, 100%);
  color: var(--color-white);
}

.card-2 {
  background-color: hsl(219, 29%, 14%);
  background-color: var(--color-dark-blue);
  color: hsl(0, 0%, 100%);
  color: var(--color-white);
}

.card-3 {
  background-color: hsl(0, 0%, 100%);
  background-color: var(--color-white);
  color: hsl(217, 19%, 35%);
  color: var(--color-gray-500);
}

.card-4 {
  background-color: hsl(219, 29%, 14%);
  background-color: var(--color-dark-blue);
  color: hsl(0, 0%, 100%);
  color: var(--color-white);
}

.card-5 {
  background-color: hsl(219, 29%, 14%);
  background-color: var(--color-dark-blue);
  color: hsl(0, 0%, 100%);
  color: var(--color-white);
}

.card-top {
  display: flex;
  align-items: center;
}

@media screen and (min-width: 768px) {
  .container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 2rem;
  }

  .card {
    flex: 1 0 auto;
    width: calc(50% - 1rem);
    min-width: 300px;
  }
}

@media screen and (min-width: 1024px) {
  .container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 2rem;
  }

  .card{
    min-width: 400px;
    width: 400px;
  }

  .card-1{
    grid-column: 1 / 3;
    grid-row: 1 / 2;
  }

  .card-2{
    grid-column: 2 / 3;
    grid-row: 1 / 2;
  }

  .card-3{
    grid-column: 1 / 2;
    grid-row: 2 / 3;
  }

  .card-4{
    grid-column: 2 / 3;
    grid-row: 2 / 3;
  }

  .card-5{
    grid-column: 3 / 4;
    grid-row: 1 / 3;
  }
  
}