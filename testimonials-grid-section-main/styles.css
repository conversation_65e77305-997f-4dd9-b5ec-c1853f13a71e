/* CSS变量定义 - 使用回退值确保兼容性 */
:root {
  /* 主色调 */
  --color-moderate-violet: hsl(263, 55%, 52%);
  --color-very-dark-grayish-blue: hsl(217, 19%, 35%);
  --color-very-dark-blackish-blue: hsl(219, 29%, 14%);
  --color-white: hsl(0, 0%, 100%);

  /* 中性色 */
  --color-light-gray: hsl(0, 0%, 81%);
  --color-light-grayish-blue: hsl(210, 46%, 95%);

  /* 字体 */
  --font-family: 'Barlow Semi Condensed', sans-serif;
  --font-size-base: 13px;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;

  /* 间距 */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 2.5rem;

  /* 其他 */
  --border-radius: 8px;
  --box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --transition: all 0.3s ease;
}

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Barlow Semi Condensed', sans-serif;
  font-family: var(--font-family, 'Barlow Semi Condensed', sans-serif);
  font-size: 13px;
  font-size: var(--font-size-base, 13px);
  font-weight: 500;
  font-weight: var(--font-weight-medium, 500);
  background-color: hsl(210, 46%, 95%);
  background-color: var(--color-light-grayish-blue, hsl(210, 46%, 95%));
  line-height: 1.5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 屏幕阅读器专用 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.container {
  flex: 1;
  padding: 2rem;
  padding: var(--spacing-lg, 2rem);
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  gap: var(--spacing-md, 1.5rem);
}

/* 卡片基础样式 */
.card {
  background-color: hsl(0, 0%, 100%);
  background-color: var(--color-white, hsl(0, 0%, 100%));
  border-radius: 8px;
  border-radius: var(--border-radius, 8px);
  padding: 2rem;
  padding: var(--spacing-lg, 2rem);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  box-shadow: var(--box-shadow, 0 25px 50px -12px rgba(0, 0, 0, 0.25));
  transition: all 0.3s ease;
  transition: var(--transition, all 0.3s ease);
  position: relative;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  margin-bottom: var(--spacing-sm, 1rem);
}

.profile-image {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 1rem;
  margin-right: var(--spacing-sm, 1rem);
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.card-author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 0.9rem;
  font-weight: 600;
  font-weight: var(--font-weight-semibold, 600);
  margin-bottom: 0.125rem;
}

.author-status {
  font-size: 0.75rem;
  opacity: 0.5;
}

/* 推荐内容 */
.testimonial {
  font-style: normal;
}

.testimonial-summary {
  font-size: 1.25rem;
  font-weight: 600;
  font-weight: var(--font-weight-semibold, 600);
  line-height: 1.2;
  margin-bottom: 1rem;
  margin-bottom: var(--spacing-sm, 1rem);
}

.testimonial-detail {
  font-size: 0.85rem;
  line-height: 1.6;
  opacity: 0.7;
}

/* 卡片特定样式 */
.card-daniel {
  background-color: hsl(263, 55%, 52%);
  background-color: var(--color-moderate-violet, hsl(263, 55%, 52%));
  color: hsl(0, 0%, 100%);
  color: var(--color-white, hsl(0, 0%, 100%));
  background-image: url('./images/bg-pattern-quotation.svg');
  background-repeat: no-repeat;
  background-position: top 0 right 2rem;
  background-size: 104px 102px;
}

.card-jonathan {
  background-color: hsl(217, 19%, 35%);
  background-color: var(--color-very-dark-grayish-blue, hsl(217, 19%, 35%));
  color: hsl(0, 0%, 100%);
  color: var(--color-white, hsl(0, 0%, 100%));
}

.card-jeanette {
  background-color: hsl(0, 0%, 100%);
  background-color: var(--color-white, hsl(0, 0%, 100%));
  color: hsl(217, 19%, 35%);
  color: var(--color-very-dark-grayish-blue, hsl(217, 19%, 35%));
}

.card-patrick {
  background-color: hsl(219, 29%, 14%);
  background-color: var(--color-very-dark-blackish-blue, hsl(219, 29%, 14%));
  color: hsl(0, 0%, 100%);
  color: var(--color-white, hsl(0, 0%, 100%));
}

.card-kira {
  background-color: hsl(0, 0%, 100%);
  background-color: var(--color-white, hsl(0, 0%, 100%));
  color: hsl(217, 19%, 35%);
  color: var(--color-very-dark-grayish-blue, hsl(217, 19%, 35%));
}

/* 深色卡片的头像边框 */
.card-daniel .profile-image,
.card-jonathan .profile-image,
.card-patrick .profile-image {
  border-color: rgba(255, 255, 255, 0.5);
}

/* 浅色卡片的头像边框 */
.card-jeanette .profile-image,
.card-kira .profile-image {
  border-color: rgba(117, 65, 200, 0.5);
}

/* Attribution样式 */
.attribution {
  margin-top: auto;
  padding: 1rem;
  text-align: center;
  font-size: 11px;
  color: hsl(217, 19%, 35%);
  color: var(--color-very-dark-grayish-blue, hsl(217, 19%, 35%));
}

/* 响应式设计 */
@media screen and (min-width: 768px) {
  .container {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    gap: var(--spacing-lg, 2rem);
  }
}

@media screen and (min-width: 1024px) {
  .container {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 2rem;
    gap: var(--spacing-lg, 2rem);
    padding: 4rem 2rem;
  }

  /* 网格布局定位 */
  .card-daniel {
    grid-column: 1 / 3;
    grid-row: 1;
  }

  .card-jonathan {
    grid-column: 3;
    grid-row: 1;
  }

  .card-jeanette {
    grid-column: 1;
    grid-row: 2;
  }

  .card-patrick {
    grid-column: 2 / 4;
    grid-row: 2;
  }

  .card-kira {
    grid-column: 4;
    grid-row: 1 / 3;
  }
}